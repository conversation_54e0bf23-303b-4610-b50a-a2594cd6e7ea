<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sale - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Remove outlines from all elements */
        *:focus,
        *:active,
        *:focus-visible {
            outline: none !important;
        }

        body {
            padding-top: 80px;
        }
        
        .sale-hero {
            background:
                radial-gradient(circle at 25% 25%, #ff6b6b 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #4ecdc4 0%, transparent 50%),
                linear-gradient(135deg, #667eea 0%, #764ba2 50%, #4B0082 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: backgroundPulse 12s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            25% { filter: hue-rotate(90deg) brightness(1.1); }
            50% { filter: hue-rotate(180deg) brightness(0.9); }
            75% { filter: hue-rotate(270deg) brightness(1.1); }
        }

        /* Matrix-style digital rain */
        .digital-rain {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
        }

        .rain-column {
            position: absolute;
            top: -100%;
            width: 2px;
            background: linear-gradient(to bottom,
                transparent 0%,
                rgba(255, 255, 255, 0.8) 50%,
                transparent 100%);
            animation: rainFall linear infinite;
        }

        @keyframes rainFall {
            0% { top: -100%; opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { top: 100%; opacity: 0; }
        }



        /* Ensure content is above background effects */
        .sale-hero .page-hero-content {
            position: relative;
            z-index: 2;
        }
        
        .sale-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        /* Believe loud animation styles */
        #believeLoudTitle {
            font-size: 4rem;
            font-weight: 700;
            color: #ffffff;
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.8),
                0 0 10px rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            letter-spacing: 2px;
        }

        /* Text Trail Animation */
        .text-trail {
            display: inline-block;
        }

        .text-trail .letter {
            display: inline-block;
            opacity: 0;
            transform: translateY(30px) scale(0.8);
            animation: letterTrail 0.8s ease-out forwards;
        }

        @keyframes letterTrail {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.8);
            }
            50% {
                opacity: 0.8;
                transform: translateY(5px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Subtle glow effect that starts after trail completes */
        @keyframes textGlow {
            0%, 100% {
                text-shadow:
                    0 2px 4px rgba(0, 0, 0, 0.8),
                    0 0 8px rgba(255, 255, 255, 0.4);
            }
            50% {
                text-shadow:
                    0 2px 4px rgba(0, 0, 0, 0.8),
                    0 0 15px rgba(255, 255, 255, 0.6),
                    0 0 25px rgba(255, 255, 255, 0.3);
            }
        }

        .text-trail.glow {
            animation: textGlow 3s ease-in-out 2.5s infinite;
        }

        /* Floating orbs */
        .floating-orbs {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .orb {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
            animation: floatOrb 8s ease-in-out infinite;
            filter: blur(1px);
        }

        .orb:nth-child(1) {
            width: 120px;
            height: 120px;
            top: 15%;
            left: 10%;
            animation-delay: 0s;
            animation-duration: 12s;
        }

        .orb:nth-child(2) {
            width: 80px;
            height: 80px;
            top: 70%;
            right: 15%;
            animation-delay: -4s;
            animation-duration: 10s;
        }

        .orb:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 40%;
            left: 70%;
            animation-delay: -8s;
            animation-duration: 14s;
        }

        .orb:nth-child(4) {
            width: 100px;
            height: 100px;
            top: 25%;
            right: 25%;
            animation-delay: -2s;
            animation-duration: 16s;
        }

        @keyframes floatOrb {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-30px) translateX(20px) scale(1.1);
                opacity: 0.5;
            }
            50% {
                transform: translateY(-10px) translateX(-15px) scale(0.9);
                opacity: 0.4;
            }
            75% {
                transform: translateY(20px) translateX(25px) scale(1.05);
                opacity: 0.6;
            }
        }

        /* Wave patterns */
        .wave-pattern {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            z-index: 1;
            overflow: hidden;
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 200%;
            height: 100px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 25%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.1) 75%,
                transparent 100%);
            animation: waveMove 6s linear infinite;
        }

        .wave:nth-child(2) {
            animation-delay: -2s;
            opacity: 0.5;
            height: 80px;
        }

        .wave:nth-child(3) {
            animation-delay: -4s;
            opacity: 0.3;
            height: 60px;
        }

        @keyframes waveMove {
            0% { transform: translateX(-50%); }
            100% { transform: translateX(0%); }
        }

        /* Rotating rings */
        .rotating-rings {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            z-index: 1;
        }

        .ring {
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: rotateRing linear infinite;
        }

        .ring:nth-child(1) {
            width: 300px;
            height: 300px;
            top: 50px;
            left: 50px;
            border-top-color: rgba(255, 255, 255, 0.3);
            animation-duration: 20s;
        }

        .ring:nth-child(2) {
            width: 200px;
            height: 200px;
            top: 100px;
            left: 100px;
            border-right-color: rgba(216, 191, 216, 0.4);
            animation-duration: 15s;
            animation-direction: reverse;
        }

        .ring:nth-child(3) {
            width: 100px;
            height: 100px;
            top: 150px;
            left: 150px;
            border-bottom-color: rgba(240, 147, 251, 0.3);
            animation-duration: 10s;
        }

        @keyframes rotateRing {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .sale-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .sale-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .filters-section {
            background: var(--section-bg);
            padding: 2rem 0;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
        
        .filters-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .filters-left {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .filter-group label {
            font-weight: 500;
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--input-bg);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
        }
        
        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }
        
        .view-btn {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .view-btn:hover {
            border-color: var(--primary-color);
            background: var(--section-bg);
        }

        .view-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .results-info {
            padding: 1rem 0;
            color: var(--text-light);
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .sale-products {
            padding: 3rem 0;
        }

        @media (max-width: 768px) {
            .sale-products {
                padding: 2rem 0;
            }
        }

        @media (max-width: 480px) {
            .sale-products {
                padding: 1.5rem 0;
            }
        }
        
        .products-grid.list-view {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .product-card.list-view {
            display: flex;
            align-items: center;
            padding: 1rem;
            gap: 1rem;
        }
        
        .product-card.list-view .product-image {
            width: 150px;
            height: 150px;
            flex-shrink: 0;
        }
        
        .product-card.list-view .product-info {
            flex: 1;
            padding: 0;
        }
        
        .product-card.list-view .product-actions {
            position: static;
            opacity: 1;
            flex-direction: row;
            gap: 0.5rem;
        }
        
        .sale-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 2;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .pagination button:hover {
            background: var(--section-bg);
            border-color: var(--primary-color);
        }

        .pagination button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .loading-products {
            text-align: center;
            padding: 3rem 0;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .loading-spinner {
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="150" height="40" viewBox="0 0 150 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="16" font-weight="700" fill="url(#logoGradient)">The Project Faith</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="sale.html" class="active">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="page-hero sale-hero">
        <!-- Digital rain effect -->
        <div class="digital-rain" id="digitalRain">
            <!-- Rain columns will be generated by JavaScript -->
        </div>

        <!-- Floating orbs -->
        <div class="floating-orbs">
            <div class="orb"></div>
            <div class="orb"></div>
            <div class="orb"></div>
            <div class="orb"></div>
        </div>

        <!-- Rotating rings -->
        <div class="rotating-rings">
            <div class="ring"></div>
            <div class="ring"></div>
            <div class="ring"></div>
        </div>

        <!-- Wave patterns -->
        <div class="wave-pattern">
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
        </div>

        <div class="page-hero-content">
            <h1 id="believeLoudTitle">Believe loud</h1>
        </div>
    </section>



    <!-- Filters Section -->
    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="filters-left">
                    <div class="filter-group">
                        <label for="categoryFilter">Category:</label>
                        <select id="categoryFilter" class="filter-select">
                            <option value="all">All Categories</option>
                            <option value="women">Women</option>
                            <option value="men">Men</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="sortFilter">Sort by:</label>
                        <select id="sortFilter" class="filter-select">
                            <option value="featured">Featured</option>
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="discount">Highest Discount</option>
                            <option value="newest">Newest</option>
                        </select>
                    </div>
                </div>
                
                <div class="view-toggle">
                    <button class="view-btn active" id="gridViewBtn">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" id="listViewBtn">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="sale-products">
        <div class="container">
            <div class="results-info">
                <span id="resultsCount">Loading products...</span>
            </div>
            
            <div class="loading-products" id="loadingProducts">
                <div class="loading-spinner"></div>
                <p>Loading sale products...</p>
            </div>
            
            <div class="products-grid" id="saleProductsGrid">
                <!-- Products will be loaded dynamically -->
            </div>
            
            <div class="pagination" id="pagination">
                <!-- Pagination will be generated dynamically -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>VAITH</h3>
                    <p>Your destination for trendy and affordable fashion.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Shipping Info</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 The Project Faith. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Cart Modal -->
    <div class="modal" id="cartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Shopping Cart</h3>
                <button class="close-modal" id="closeCart">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="cartItems">
                <!-- Cart items will be loaded dynamically -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <strong>Total: $<span id="cartTotal">0.00</span></strong>
                </div>
                <button class="checkout-btn">Checkout</button>
            </div>
        </div>
    </div>

    <!-- Favorites Modal -->
    <div class="modal" id="favoritesModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Your Favorites</h3>
                <button class="close-modal" id="closeFavorites">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="favoritesItems">
                <!-- Favorites will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/vip-access.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>
    
    <script>
        // Sale page specific functionality
        let currentPage = 1;
        let currentView = 'grid';
        let currentFilters = {
            category: '',
            sort: 'featured'
        };
        
        // Sample sale products data
        const saleProducts = [
            {
                id: 1,
                title: "Summer Floral Dress",
                price: 29.99,
                originalPrice: 49.99,
                image: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=500&fit=crop",
                rating: 4.5,
                reviews: 128,
                category: "women",
                onSale: true
            },
            {
                id: 2,
                title: "Classic White Shirt",
                price: 24.99,
                originalPrice: 34.99,
                image: "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400&h=500&fit=crop",
                rating: 4.2,
                reviews: 89,
                category: "men",
                onSale: true
            },

            // Add more sale products...
            {
                id: 7,
                title: "Striped T-Shirt",
                price: 15.99,
                originalPrice: 24.99,
                image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=500&fit=crop",
                rating: 4.3,
                reviews: 67,
                category: "women",
                onSale: true
            },
            {
                id: 8,
                title: "Denim Jeans",
                price: 39.99,
                originalPrice: 59.99,
                image: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=500&fit=crop",
                rating: 4.6,
                reviews: 143,
                category: "men",
                onSale: true
            }
        ];
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeSalePage();
        });
        
        function initializeSalePage() {
            // Initialize filters
            document.getElementById('categoryFilter').addEventListener('change', handleFilterChange);
            document.getElementById('sortFilter').addEventListener('change', handleFilterChange);

            // Initialize view toggle
            document.getElementById('gridViewBtn').addEventListener('click', () => setView('grid'));
            document.getElementById('listViewBtn').addEventListener('click', () => setView('list'));

            // Load initial products
            loadProducts();
        }
        
        function handleFilterChange() {
            currentFilters.category = document.getElementById('categoryFilter').value;
            currentFilters.sort = document.getElementById('sortFilter').value;
            currentPage = 1;
            loadProducts();
        }
        
        function setView(view) {
            currentView = view;
            
            // Update button states
            document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
            document.getElementById('listViewBtn').classList.toggle('active', view === 'list');
            
            // Update grid class
            const grid = document.getElementById('saleProductsGrid');
            grid.classList.toggle('list-view', view === 'list');
            
            // Re-render products with new view
            renderProducts(getFilteredProducts());
        }
        
        function loadProducts() {
            const loadingElement = document.getElementById('loadingProducts');
            const gridElement = document.getElementById('saleProductsGrid');
            
            // Show loading
            loadingElement.style.display = 'block';
            gridElement.style.display = 'none';
            
            // Simulate API call
            setTimeout(() => {
                const filteredProducts = getFilteredProducts();
                renderProducts(filteredProducts);
                updateResultsInfo(filteredProducts.length);
                renderPagination(filteredProducts.length);
                
                // Hide loading
                loadingElement.style.display = 'none';
                gridElement.style.display = currentView === 'list' ? 'flex' : 'grid';
            }, 500);
        }
        
        function getFilteredProducts() {
            let filtered = [...saleProducts];

            // Filter by category
            if (currentFilters.category) {
                filtered = filtered.filter(product => product.category === currentFilters.category);
            }

            // Sort products
            switch (currentFilters.sort) {
                case 'price-low':
                    filtered.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    filtered.sort((a, b) => b.price - a.price);
                    break;
                case 'discount':
                    filtered.sort((a, b) => {
                        const discountA = a.originalPrice ? (a.originalPrice - a.price) / a.originalPrice : 0;
                        const discountB = b.originalPrice ? (b.originalPrice - b.price) / b.originalPrice : 0;
                        return discountB - discountA;
                    });
                    break;
                case 'newest':
                    // In a real app, this would sort by date
                    filtered.reverse();
                    break;
                default:
                    // Featured - keep original order
                    break;
            }
            
            return filtered;
        }
        
        function renderProducts(products) {
            const container = document.getElementById('saleProductsGrid');
            container.innerHTML = '';

            const itemsPerPage = 12;
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProducts = products.slice(startIndex, endIndex);

            pageProducts.forEach(product => {
                const productCard = createSaleProductCard(product);
                container.appendChild(productCard);
            });

            // Update button states after rendering products
            setTimeout(() => {
                if (typeof updateFavoriteButtons === 'function') {
                    updateFavoriteButtons();
                }
                if (typeof updateCartButtons === 'function') {
                    updateCartButtons();
                }
            }, 100);
        }
        
        function createSaleProductCard(product) {
            const card = document.createElement('div');
            card.className = `product-card ${currentView === 'list' ? 'list-view' : ''}`;
            card.setAttribute('data-product-id', product.id);

            const discount = product.originalPrice ? 
                Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;

            card.innerHTML = `
                <div class="product-image">
                    <img src="${product.image}" alt="${product.title}" loading="lazy">
                    <div class="sale-badge">-${discount}%</div>
                    <div class="product-actions">
                        <button class="action-btn favorite-btn" data-product-id="${product.id}">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn quick-view-btn" data-product-id="${product.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.title}</h3>
                    <div class="product-price">
                        <span class="current-price">$${product.price}</span>
                        <span class="original-price">$${product.originalPrice}</span>
                    </div>
                    <div class="product-rating">
                        <div class="stars interactive-stars" data-product-id="${product.id}">
                            ${generateInteractiveStars(product.rating, product.id)}
                        </div>
                        <span class="rating-count">(${product.reviews})</span>
                    </div>
                    <button class="btn btn-primary favorite-btn-text" data-product-id="${product.id}">
                        <i class="far fa-heart"></i>
                        Add to Favorites
                    </button>
                </div>
            `;

            // Add event listeners
            const favoriteBtn = card.querySelector('.favorite-btn');
            const favoriteBtnText = card.querySelector('.favorite-btn-text');
            const quickViewBtn = card.querySelector('.quick-view-btn');

            favoriteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(product.id);
                // Update button states after toggling favorite
                if (typeof updateFavoriteButtons === 'function') {
                    updateFavoriteButtons();
                }
            });

            favoriteBtnText.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(product.id);
                // Update button states after toggling favorite
                if (typeof updateFavoriteButtons === 'function') {
                    updateFavoriteButtons();
                }
            });

            // Interactive rating functionality
            const interactiveStars = card.querySelector('.interactive-stars');
            if (interactiveStars && typeof setupInteractiveRating === 'function') {
                setupInteractiveRating(interactiveStars, product.id);
            }

            quickViewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                openProductModal(product);
            });

            // Navigate to product page on card click
            card.addEventListener('click', (e) => {
                if (!e.target.closest('button') && !e.target.closest('.interactive-stars')) {
                    window.location.href = `product.html?id=${product.id}`;
                }
            });

            return card;
        }
        
        function updateResultsInfo(totalResults) {
            const resultsElement = document.getElementById('resultsCount');
            resultsElement.textContent = `Showing ${totalResults} sale items`;
        }
        
        function renderPagination(totalItems) {
            const container = document.getElementById('pagination');
            const itemsPerPage = 12;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }
            
            let paginationHTML = '';
            
            // Previous button
            paginationHTML += `
                <button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
            
            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button class="${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<span>...</span>';
                }
            }
            
            // Next button
            paginationHTML += `
                <button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
            
            container.innerHTML = paginationHTML;
        }
        
        function changePage(page) {
            currentPage = page;
            loadProducts();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        // Make changePage available globally
        window.changePage = changePage;

        // Initialize text trail animation for "Believe loud"
        function initializeBelieveLoudAnimation() {
            const titleElement = document.getElementById('believeLoudTitle');
            if (!titleElement) return;

            const text = 'Believe loud';
            titleElement.innerHTML = '';

            // Create container for text trail effect
            const trailContainer = document.createElement('span');
            trailContainer.className = 'text-trail glow';

            // Split text into individual letters and spaces
            text.split('').forEach((char, index) => {
                const letterSpan = document.createElement('span');
                letterSpan.className = 'letter';
                letterSpan.textContent = char === ' ' ? '\u00A0' : char; // Use non-breaking space

                // Add staggered delay for trail effect
                letterSpan.style.animationDelay = `${index * 0.1}s`;

                trailContainer.appendChild(letterSpan);
            });

            titleElement.appendChild(trailContainer);
        }

        // Initialize digital rain effect
        function initializeDigitalRain() {
            const rainContainer = document.getElementById('digitalRain');
            if (!rainContainer) return;

            const numberOfColumns = 50;

            for (let i = 0; i < numberOfColumns; i++) {
                const column = document.createElement('div');
                column.className = 'rain-column';
                column.style.left = `${(i / numberOfColumns) * 100}%`;
                column.style.height = `${Math.random() * 100 + 50}px`;
                column.style.animationDuration = `${Math.random() * 3 + 2}s`;
                column.style.animationDelay = `${Math.random() * 5}s`;
                rainContainer.appendChild(column);
            }
        }

        // Initialize animation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeBelieveLoudAnimation();
            initializeDigitalRain();
        });

        // Generate interactive star rating for product cards
        function generateInteractiveStars(rating, productId) {
            let stars = '';
            const userRating = getUserProductRating(productId);
            const displayRating = userRating || rating;

            for (let i = 1; i <= 5; i++) {
                const isActive = i <= Math.floor(displayRating);
                const isHalf = i === Math.ceil(displayRating) && displayRating % 1 !== 0;

                if (isActive) {
                    stars += `<i class="fas fa-star interactive-star" data-rating="${i}" data-product-id="${productId}"></i>`;
                } else if (isHalf) {
                    stars += `<i class="fas fa-star-half-alt interactive-star" data-rating="${i}" data-product-id="${productId}"></i>`;
                } else {
                    stars += `<i class="far fa-star interactive-star" data-rating="${i}" data-product-id="${productId}"></i>`;
                }
            }

            return stars;
        }

        // Setup interactive rating functionality
        function setupInteractiveRating(starsContainer, productId) {
            const stars = starsContainer.querySelectorAll('.interactive-star');

            stars.forEach((star, index) => {
                // Hover effect
                star.addEventListener('mouseenter', () => {
                    highlightStarsUpTo(stars, index + 1);
                });

                // Click to rate
                star.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const rating = index + 1;
                    submitProductRating(productId, rating);
                    updateStarDisplay(stars, rating);
                    if (typeof showNotification === 'function') {
                        showNotification(`You rated this product ${rating} star${rating > 1 ? 's' : ''}!`, 'success');
                    }
                });
            });

            // Reset on mouse leave
            starsContainer.addEventListener('mouseleave', () => {
                const userRating = getUserProductRating(productId);
                if (userRating) {
                    updateStarDisplay(stars, userRating);
                } else {
                    resetStarDisplay(stars);
                }
            });
        }

        // Highlight stars up to a certain rating
        function highlightStarsUpTo(stars, rating) {
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.className = 'fas fa-star interactive-star highlighted';
                } else {
                    star.className = 'far fa-star interactive-star';
                }
            });
        }

        // Update star display after rating
        function updateStarDisplay(stars, rating) {
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.className = 'fas fa-star interactive-star rated';
                } else {
                    star.className = 'far fa-star interactive-star';
                }
            });
        }

        // Reset star display
        function resetStarDisplay(stars) {
            stars.forEach(star => {
                star.className = star.className.replace(' highlighted', '').replace(' rated', '');
            });
        }

        // Submit product rating
        function submitProductRating(productId, rating) {
            const userRatings = JSON.parse(localStorage.getItem('userProductRatings')) || {};
            userRatings[productId] = {
                rating: rating,
                date: new Date().toISOString()
            };
            localStorage.setItem('userProductRatings', JSON.stringify(userRatings));

            // Update the product's average rating
            updateProductAverageRating(productId, rating);
        }

        // Get user's rating for a specific product
        function getUserProductRating(productId) {
            const userRatings = JSON.parse(localStorage.getItem('userProductRatings')) || {};
            return userRatings[productId] ? userRatings[productId].rating : null;
        }

        // Update product's average rating (simplified - in real app this would be server-side)
        function updateProductAverageRating(productId, newRating) {
            const productRatings = JSON.parse(localStorage.getItem('productRatings')) || {};

            if (!productRatings[productId]) {
                productRatings[productId] = {
                    totalRating: 0,
                    totalReviews: 0,
                    average: 0
                };
            }

            productRatings[productId].totalRating += newRating;
            productRatings[productId].totalReviews += 1;
            productRatings[productId].average = productRatings[productId].totalRating / productRatings[productId].totalReviews;

            localStorage.setItem('productRatings', JSON.stringify(productRatings));
        }
    </script>
</body>
</html>
