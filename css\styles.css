/* Reset and Base Styles */
html {
    scroll-behavior: smooth;
}

:root {
    /* Light Theme Colors */
    --primary-color: #4B0082;      /* Dark Purple */
    --secondary-color: #D8BFD8;    /* Light Purple (Lavender) */
    --background-color: #FFFFFF;   /* White */
    --text-color: #333;
    --text-light: #666;
    --border-color: #e1e5e9;
    --success-color: #27ae60;
    --warning-color: #ffc107;
    --error-color: #e74c3c;

    /* Theme-aware colors */
    --card-bg: #ffffff;
    --card-border: #e1e5e9;
    --input-bg: #ffffff;
    --input-border: #e1e5e9;
    --modal-bg: #ffffff;
    --modal-overlay: rgba(0, 0, 0, 0.5);
    --navbar-bg: rgba(255, 255, 255, 0.95);
    --navbar-border: rgba(255, 255, 255, 0.3);
    --section-bg: #f8f9fa;
    --footer-bg: #1a202c;
    --footer-text: #f7fafc;

    /* Modern Hero Variables */
    --hero-gradient-1: #667eea;
    --hero-gradient-2: #764ba2;
    --hero-gradient-3: #f093fb;
    --hero-text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --hero-glass-bg: rgba(255, 255, 255, 0.1);
    --hero-glass-border: rgba(255, 255, 255, 0.3);
    --hero-animation-duration: 15s;
    --hero-transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* Enhanced Spacing Variables */
    --hero-padding-mobile: 2rem 0;
    --hero-padding-tablet: 4rem 0;
    --hero-padding-desktop: 6rem 0;
    --hero-gap-mobile: 2rem;
    --hero-gap-tablet: 4rem;
    --hero-gap-desktop: 6rem;

    /* Section Spacing Variables */
    --section-padding-mobile: 3rem 0;
    --section-padding-tablet: 4rem 0;
    --section-padding-desktop: 6rem 0;
    --section-margin-mobile: 1rem 0;
    --section-margin-tablet: 2rem 0;
    --section-margin-desktop: 3rem 0;

    /* Container Spacing Variables */
    --container-padding-mobile: 0 1rem;
    --container-padding-tablet: 0 2rem;
    --container-padding-desktop: 0 2rem;

    /* Element Spacing Variables */
    --element-gap-small: 0.5rem;
    --element-gap-medium: 1rem;
    --element-gap-large: 1.5rem;
    --element-gap-xlarge: 2rem;

    /* Form Spacing Variables */
    --form-element-margin: 1.5rem;
    --form-input-padding: 0.75rem 1rem;
    --form-button-padding: 0.75rem 1.5rem;

    /* Admin Dashboard Variables */
    --admin-sidebar-width: 280px;
    --admin-sidebar-collapsed: 80px;
    --admin-header-height: 70px;
    --admin-card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --admin-card-hover-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Status Colors */
    --status-active: #10b981;
    --status-inactive: #6b7280;
    --status-pending: #f59e0b;
    --status-suspended: #ef4444;
    --status-completed: #059669;
    --status-cancelled: #dc2626;
}

/* Dark Theme Colors */
[data-theme="dark"],
body.dark-theme {
    --background-color: #0f0f0f;
    --text-color: #e4e4e7;
    --text-light: #a1a1aa;
    --border-color: #27272a;
    --card-bg: #18181b;
    --card-border: #27272a;
    --input-bg: #18181b;
    --input-border: #3f3f46;
    --modal-bg: #18181b;
    --modal-overlay: rgba(0, 0, 0, 0.8);
    --navbar-bg: rgba(24, 24, 27, 0.95);
    --navbar-border: rgba(63, 63, 70, 0.3);
    --section-bg: #09090b;
    --footer-bg: #0f0f0f;
    --footer-text: #f4f4f5;

    /* Adjust hero glass effects for dark mode */
    --hero-glass-bg: rgba(0, 0, 0, 0.2);
    --hero-glass-border: rgba(255, 255, 255, 0.1);

    /* Enhanced dark mode colors for better contrast */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --link-color: #8b5cf6;
    --link-hover-color: #a78bfa;
}

/* Dark mode specific component styling */
[data-theme="dark"] .hero-badge,
body.dark-theme .hero-badge {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .cta-btn.secondary,
body.dark-theme .cta-btn.secondary {
    background: rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .cta-btn.secondary:hover,
body.dark-theme .cta-btn.secondary:hover {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .category-card,
body.dark-theme .category-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .category-overlay,
body.dark-theme .category-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.3));
}

[data-theme="dark"] .section-title,
body.dark-theme .section-title {
    color: var(--text-color);
}

/* Dark mode newsletter styling */
[data-theme="dark"] .newsletter-form input,
body.dark-theme .newsletter-form input {
    background: var(--input-bg);
    color: var(--text-color);
    border: 1px solid var(--input-border);
}

[data-theme="dark"] .newsletter-form input::placeholder,
body.dark-theme .newsletter-form input::placeholder {
    color: var(--text-light);
}

[data-theme="dark"] .newsletter-form input:focus,
body.dark-theme .newsletter-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
}

/* Dark mode for trending section */
[data-theme="dark"] .trending,
body.dark-theme .trending {
    background-color: var(--section-bg);
}

[data-theme="dark"] .trending-tags .tag,
body.dark-theme .trending-tags .tag {
    background: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .trending-tags .tag:hover,
body.dark-theme .trending-tags .tag:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Dark mode for featured products section */
[data-theme="dark"] .featured-products,
body.dark-theme .featured-products {
    background-color: var(--background-color);
}

/* Dark mode for newsletter section */
[data-theme="dark"] .newsletter,
body.dark-theme .newsletter {
    background: linear-gradient(135deg, #18181b 0%, #27272a 100%);
}

[data-theme="dark"] .newsletter h2,
body.dark-theme .newsletter h2 {
    color: var(--text-color);
}

[data-theme="dark"] .newsletter p,
body.dark-theme .newsletter p {
    color: var(--text-light);
}

[data-theme="dark"] .newsletter-form button,
body.dark-theme .newsletter-form button {
    background: var(--primary-color);
    color: white;
    border: none;
}

[data-theme="dark"] .newsletter-form button:hover,
body.dark-theme .newsletter-form button:hover {
    background: #6a1b9a;
    transform: translateY(-2px);
}

/* Dark mode for product cards */
[data-theme="dark"] .product-card,
body.dark-theme .product-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .product-card:hover,
body.dark-theme .product-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    border-color: var(--primary-color);
}

[data-theme="dark"] .product-title,
body.dark-theme .product-title {
    color: var(--text-color);
}

[data-theme="dark"] .product-info,
body.dark-theme .product-info {
    background: var(--card-bg);
}

[data-theme="dark"] .current-price,
body.dark-theme .current-price {
    color: var(--primary-color);
}

[data-theme="dark"] .original-price,
body.dark-theme .original-price {
    color: var(--text-light);
}

[data-theme="dark"] .rating-count,
body.dark-theme .rating-count {
    color: var(--text-light);
}

[data-theme="dark"] .action-btn,
body.dark-theme .action-btn {
    background: rgba(24, 24, 27, 0.9);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .action-btn:hover,
body.dark-theme .action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-outline,
body.dark-theme .btn-outline {
    border-color: var(--border-color);
    color: var(--text-color);
    background: transparent;
}

[data-theme="dark"] .btn-outline:hover,
body.dark-theme .btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(75, 0, 130, 0.1);
}

/* Dark mode for hero elements */
[data-theme="dark"] .hero-stats,
body.dark-theme .hero-stats {
    color: white;
}

/* Dark mode for page hero sections */
[data-theme="dark"] .page-hero,
body.dark-theme .page-hero {
    background: linear-gradient(135deg, #18181b 0%, #27272a 25%, var(--primary-color) 50%, #3f3f46 75%, #52525b 100%);
}

[data-theme="dark"] .page-hero-content h1,
[data-theme="dark"] .page-hero-content p,
body.dark-theme .page-hero-content h1,
body.dark-theme .page-hero-content p {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .floating-card,
body.dark-theme .floating-card {
    background: rgba(24, 24, 27, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

[data-theme="dark"] .scroll-indicator,
body.dark-theme .scroll-indicator {
    color: rgba(255, 255, 255, 0.8);
}

[data-theme="dark"] .scroll-indicator span,
body.dark-theme .scroll-indicator span {
    color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .scroll-arrow,
body.dark-theme .scroll-arrow {
    color: rgba(255, 255, 255, 0.8);
}

/* Dark mode for discount badges and other elements */
[data-theme="dark"] .discount-badge,
body.dark-theme .discount-badge {
    background: var(--primary-color);
    color: white;
}

[data-theme="dark"] .hero-cta-group,
body.dark-theme .hero-cta-group {
    /* Ensure CTA buttons work well in dark mode */
}

[data-theme="dark"] .stat-number,
body.dark-theme .stat-number {
    color: white;
}

[data-theme="dark"] .stat-label,
body.dark-theme .stat-label {
    color: rgba(255, 255, 255, 0.8);
}

/* Dark mode accessibility improvements */
[data-theme="dark"] a,
body.dark-theme a {
    color: var(--link-color, #8b5cf6);
}

[data-theme="dark"] a:hover,
body.dark-theme a:hover {
    color: var(--link-hover-color, #a78bfa);
}

/* Dark mode focus styles for better accessibility */
[data-theme="dark"] *:focus-visible,
body.dark-theme *:focus-visible {
    outline: 2px solid #a78bfa;
    outline-offset: 2px;
}

[data-theme="dark"] button:focus-visible,
body.dark-theme button:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus,
body.dark-theme input:focus,
body.dark-theme textarea:focus,
body.dark-theme select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

/* Dark mode for form elements */
[data-theme="dark"] select,
body.dark-theme select {
    background: var(--input-bg);
    color: var(--text-color);
    border: 1px solid var(--input-border);
}

[data-theme="dark"] option,
body.dark-theme option {
    background: var(--input-bg);
    color: var(--text-color);
}

/* Dark mode for filter sections */
[data-theme="dark"] .filters-section,
body.dark-theme .filters-section {
    background: var(--section-bg);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .filter-group label,
body.dark-theme .filter-group label {
    color: var(--text-color);
}

[data-theme="dark"] .filter-select,
body.dark-theme .filter-select {
    background: var(--input-bg);
    color: var(--text-color);
    border: 1px solid var(--input-border);
}

[data-theme="dark"] .filter-select:focus,
body.dark-theme .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
}

/* Dark mode for results info */
[data-theme="dark"] .results-info,
body.dark-theme .results-info {
    color: var(--text-light);
}

/* Dark mode for loading elements */
[data-theme="dark"] .loading-products,
body.dark-theme .loading-products {
    color: var(--text-light);
}

[data-theme="dark"] .loading-spinner,
body.dark-theme .loading-spinner {
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
}

/* Dark mode selection styles */
[data-theme="dark"] ::selection,
body.dark-theme ::selection {
    background: rgba(139, 92, 246, 0.3);
    color: var(--text-color);
}

/* Dark mode scrollbar styles for webkit browsers */
[data-theme="dark"] ::-webkit-scrollbar,
body.dark-theme ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track,
body.dark-theme ::-webkit-scrollbar-track {
    background: var(--card-bg);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb,
body.dark-theme ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover,
body.dark-theme ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Remove outlines from all interactive elements */
*:focus,
*:active,
*:focus-visible {
    outline: none !important;
}

/* Remove outlines from buttons */
button:focus,
button:active,
button:focus-visible {
    outline: none !important;
}

/* Remove outlines from links */
a:focus,
a:active,
a:focus-visible {
    outline: none !important;
}

/* Remove outlines from inputs */
input:focus,
input:active,
input:focus-visible,
textarea:focus,
textarea:active,
textarea:focus-visible,
select:focus,
select:active,
select:focus-visible {
    outline: none !important;
}

/* Remove outlines from other interactive elements */
div:focus,
div:active,
span:focus,
span:active {
    outline: none !important;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Force dark mode styles when data-theme="dark" */
[data-theme="dark"] body {
    background-color: #0f0f0f !important;
    color: #e4e4e7 !important;
}

/* Force dark mode styles when body has dark-theme class */
body.dark-theme {
    background-color: #0f0f0f !important;
    color: #e4e4e7 !important;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding-desktop);
    width: 100%;
    position: relative;
}

/* Responsive container padding */
@media (max-width: 768px) {
    .container {
        padding: var(--container-padding-tablet);
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--container-padding-mobile);
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.2rem;
    font-weight: 700;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #3a0066;
    transform: translateY(-2px);
}

/* Modern CTA Button with Advanced Effects */
.cta-btn {
    background: linear-gradient(135deg,
        var(--primary-color) 0%,
        #6a1b9a 50%,
        #8e24aa 100%);
    color: white;
    padding: 18px 40px;
    border: none;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 8px 25px rgba(75, 0, 130, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Glassmorphism effect for button */
.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.6s ease;
}

.cta-btn:hover::before {
    left: 100%;
}

.cta-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(75, 0, 130, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg,
        #6a1b9a 0%,
        var(--primary-color) 50%,
        #9c27b0 100%);
}

.cta-btn:active {
    transform: translateY(-2px) scale(1.02);
    transition: all 0.1s ease;
}

/* Hero Section - Super Modern Design */
.hero {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        var(--primary-color) 50%,
        #f093fb 75%,
        var(--secondary-color) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    height: auto;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    margin-top: 0;
    padding: 0;
    width: 100%;
}

/* Page Hero (for other pages) */
.page-hero {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        var(--primary-color) 50%,
        #f093fb 75%,
        var(--secondary-color) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 50vh;
    height: auto;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    margin-top: 0;
    padding: 0;
    width: 100%;
}

.page-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    padding: 2rem;
    width: 100%;
}

.page-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: var(--hero-text-shadow);
}

.page-hero-content p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .page-hero {
        min-height: 40vh;
    }

    .page-hero-content h1 {
        font-size: 2.5rem;
    }

    .page-hero-content p {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .page-hero {
        min-height: 30vh;
    }

    .page-hero-content h1 {
        font-size: 2rem;
    }

    .page-hero-content p {
        font-size: 1rem;
    }
}



/* Glassmorphism overlay */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 1;
}

/* Floating particles background */
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: floatingParticles 20s ease-in-out infinite;
    z-index: 1;
}

@keyframes floatingParticles {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

.hero-slider {
    width: 100%;
    position: relative;
    z-index: 2;
}

.hero-slide {
    display: none;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 6rem;
    padding: 6rem 0;
    position: relative;
    width: 100%;
    min-height: 70vh;
}

.hero-slide.active {
    display: grid;
    animation: slideIn 1s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content {
    color: white;
    z-index: 3;
    position: relative;
}

/* Modern typography with enhanced styling */
.hero-content h1 {
    font-size: 4.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    line-height: 1.1;
    background: linear-gradient(45deg,
        #ffffff 0%,
        #f0f8ff 20%,
        #e6e6fa 40%,
        #ffffff 60%,
        #f5f5dc 80%,
        #ffffff 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: believeLoudAnimation 4s ease-in-out infinite, gradientShift 6s ease infinite;
    position: relative;
    overflow: hidden;
}

@keyframes believeLoudAnimation {
    0% {
        filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
        transform: scale(1) rotateX(0deg);
    }
    25% {
        filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.5));
        transform: scale(1.02) rotateX(2deg);
    }
    50% {
        filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.7));
        transform: scale(1.05) rotateX(0deg);
    }
    75% {
        filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.5));
        transform: scale(1.02) rotateX(-2deg);
    }
    100% {
        filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
        transform: scale(1) rotateX(0deg);
    }
}

/* Removed pulsing background effect for cleaner text */

/* Legacy textGlow animation for fallback */
@keyframes textGlow {
    from { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3)); }
    to { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)); }
}



.hero-content p {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    font-weight: 400;
    line-height: 1.6;
    max-width: 500px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Enhanced hero image with modern effects */
.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image img {
    width: 100%;
    height: 600px;
    max-height: 70vh;
    object-fit: cover;
    border-radius: 30px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    transition: all 0.5s ease;
    position: relative;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 35px;
    z-index: -1;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.hero-image:hover img {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Modern Hero Elements */
.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 8px 20px;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-cta-group {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.cta-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 3rem;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Image Container Enhancements */
.image-container {
    position: relative;
}

.image-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 3;
}

.floating-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: floatingCard 3s ease-in-out infinite;
}

@keyframes floatingCard {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.card-text {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.card-icon {
    font-size: 1.2rem;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    z-index: 3;
    animation: fadeInUp 1s ease-out 1s both;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.scroll-indicator span {
    font-size: 0.8rem;
    font-weight: 500;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 20px rgba(75, 0, 130, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(75, 0, 130, 0.4);
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

.scroll-to-top:active {
    transform: translateY(-2px);
}

/* Dark mode scroll to top button */
[data-theme="dark"] .scroll-to-top,
body.dark-theme .scroll-to-top {
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
}

[data-theme="dark"] .scroll-to-top:hover,
body.dark-theme .scroll-to-top:hover {
    box-shadow: 0 8px 30px rgba(139, 92, 246, 0.4);
}

/* Responsive scroll to top button */
@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .scroll-to-top {
        bottom: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Accessibility improvements for scroll to top */
.scroll-to-top:focus {
    outline: 3px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

/* Reduce motion for scroll to top */
@media (prefers-reduced-motion: reduce) {
    .scroll-to-top {
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .scroll-to-top:hover {
        transform: none;
    }
}

/* Fade in animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Performance Optimizations */
.hero,
.hero-slide,
.hero-content,
.hero-image {
    will-change: transform;
}

.hero-image img {
    will-change: transform, box-shadow;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .hero,
    .hero::before,
    .hero::after,
    .hero-content h1,
    .floating-card,
    .scroll-arrow,
    .hero-badge,
    .hero-cta-group,
    .hero-stats {
        animation: none !important;
        transition: none !important;
    }

    .cta-btn:hover,
    .hero-image:hover img {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hero-content h1 {
        -webkit-text-fill-color: white;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }

    .hero-badge,
    .floating-card {
        border: 2px solid white;
    }

    .cta-btn {
        border: 2px solid white;
    }
}

/* Focus styles for accessibility */
.cta-btn:focus-visible {
    outline: 3px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

/* Preload critical animations */
.hero-slide.active .hero-content > * {
    animation-fill-mode: both;
}

/* Sales Section with Chroma Grid */
.sales-section {
    padding: var(--section-padding-desktop);
    width: 100%;
    margin: var(--section-margin-desktop);
    position: relative;
}

.sales-section .container {
    position: relative;
}

.sales-section .section-title {
    margin-bottom: 3rem;
}

.chroma-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    width: 100%;
}

.chroma-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 300px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.chroma-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.chroma-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.chroma-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.chroma-card:hover .chroma-image img {
    transform: scale(1.1);
}

.chroma-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4), transparent);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    text-align: center;
    color: white;
    z-index: 3;
    transition: all 0.3s ease;
}

.chroma-icon {
    font-size: 2rem;
    margin-bottom: 0.8rem;
    opacity: 0.9;
    transition: all 0.3s ease;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.chroma-card:hover .chroma-icon {
    transform: scale(1.1) rotateY(360deg);
    opacity: 1;
}

.chroma-content h3 {
    font-size: 1.4rem;
    margin-bottom: 0.3rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
}

.chroma-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.95;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    font-weight: 500;
}

.chroma-link {
    display: inline-block;
    padding: 0.6rem 1.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
    border: 1px solid var(--primary-color);
    transition: all 0.3s ease;
}

.chroma-link:hover {
    background: #3a0066;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(75, 0, 130, 0.3);
    border-color: #3a0066;
}

/* Products Grid */
.featured-products, .trending {
    padding: var(--section-padding-desktop);
    width: 100%;
    margin: var(--section-margin-desktop);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--element-gap-xlarge);
    width: 100%;
    margin-top: var(--element-gap-xlarge);
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 300px;
    width: 100%;
    min-height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: white;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.original-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

.discount-badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 1rem;
}

.stars {
    color: #ffc107;
}

.rating-count {
    color: #666;
    font-size: 0.9rem;
}

/* Interactive Stars Styling */
.interactive-stars {
    cursor: pointer;
    user-select: none;
}

.interactive-star {
    transition: all 0.2s ease;
    cursor: pointer;
    margin-right: 2px;
}

.interactive-star:hover {
    transform: scale(1.1);
    color: #ffb400 !important;
}

.interactive-star.highlighted {
    color: #ffb400 !important;
    transform: scale(1.05);
}

.interactive-star.rated {
    color: #ff6b35 !important;
    text-shadow: 0 0 5px rgba(255, 107, 53, 0.3);
}

/* Rating feedback animation */
@keyframes ratingPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.interactive-star.rating-submitted {
    animation: ratingPulse 0.3s ease;
}

/* Enhanced product card actions */
.product-card-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-outline {
    background: transparent;
    border: 2px solid #ddd;
    color: #666;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    border-color: #4B0082;
    color: #4B0082;
    background: rgba(75, 0, 130, 0.05);
}

.btn-outline.favorited {
    background: #4B0082;
    border-color: #4B0082;
    color: white;
}

.btn-outline.favorited:hover {
    background: #3a0066;
    border-color: #3a0066;
}

/* Cart button active state - Remove from Cart */
.add-to-cart-btn.in-cart {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.add-to-cart-btn.in-cart:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
    color: white !important;
}

/* Enhanced product actions in image overlay */
.product-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: #4B0082;
    color: white;
    transform: scale(1.1);
}

/* Trending Tags */
.trending-tags {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tag {
    background: #f8f9fa;
    color: #666;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tag:hover {
    background: var(--primary-color);
    color: white;
}

/* Newsletter Section */
.newsletter {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: var(--section-padding-desktop);
    color: white;
    width: 100%;
    margin: var(--section-margin-desktop);
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-content h2 {
    margin-bottom: 1rem;
}

.newsletter-content p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
}

.newsletter-form button {
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-form button:hover {
    background: #3a0066;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden {
    display: none;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Section Spacing */
@media (max-width: 768px) {
    .sales-section,
    .featured-products,
    .trending,
    .newsletter {
        padding: var(--section-padding-tablet);
        margin: var(--section-margin-tablet);
    }

    .chroma-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 1.5rem;
    }

    .chroma-card {
        height: 250px;
    }

    .chroma-content {
        padding: 1.5rem;
    }

    .chroma-icon {
        font-size: 1.8rem;
        margin-bottom: 0.6rem;
    }

    .chroma-content h3 {
        font-size: 1.2rem;
        margin-bottom: 0.3rem;
    }

    .chroma-content p {
        font-size: 0.85rem;
        margin-bottom: 0.8rem;
    }

    .products-grid {
        gap: var(--element-gap-large);
        margin-top: var(--element-gap-large);
    }
}

@media (max-width: 480px) {
    .sales-section,
    .featured-products,
    .trending,
    .newsletter {
        padding: var(--section-padding-mobile);
        margin: var(--section-margin-mobile);
    }

    .chroma-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .chroma-card {
        height: 220px;
        transform-style: initial;
    }

    .chroma-card:hover {
        transform: translateY(-5px);
    }

    .chroma-content {
        padding: 1rem;
    }

    .chroma-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .chroma-content h3 {
        font-size: 1rem;
        margin-bottom: 0.2rem;
    }

    .chroma-content p {
        font-size: 0.75rem;
        margin-bottom: 0.6rem;
    }

    .chroma-link {
        padding: 0.4rem 1rem;
        font-size: 0.75rem;
    }

    .products-grid {
        gap: var(--element-gap-medium);
        margin-top: var(--element-gap-medium);
    }
}

/* Featured Products */
.featured-products {
    padding: var(--section-padding-desktop);
    width: 100%;
    margin: var(--section-margin-desktop);
}












/* Unified Moving Background for Sections */
.section-with-bg {
    position: relative;
    overflow: hidden;
}

.section-with-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background:
        radial-gradient(circle at 20% 30%, rgba(75, 0, 130, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(216, 191, 216, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.04) 0%, transparent 45%);
    background-size:
        400px 400px,
        350px 350px,
        500px 500px;
    animation:
        lightFloat1 20s ease-in-out infinite,
        lightFloat2 25s ease-in-out infinite reverse,
        lightFloat3 22s ease-in-out infinite;
    transform: translateZ(0);
}

.section-with-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background:
        linear-gradient(45deg, transparent 40%, rgba(75, 0, 130, 0.03) 60%, transparent 80%);
    background-size: 300px 300px;
    animation: gentleFlow 18s linear infinite;
    transform: translateZ(0);
}

/* Optimized Light Animations */
@keyframes lightFloat1 {
    0%, 100% {
        background-position: 0% 0%, 100% 100%, 50% 50%;
        opacity: 0.6;
    }
    50% {
        background-position: 20% 20%, 80% 80%, 60% 40%;
        opacity: 0.8;
    }
}

@keyframes lightFloat2 {
    0%, 100% {
        background-position: 100% 0%, 0% 100%, 30% 70%;
        transform: translateX(0);
    }
    50% {
        background-position: 80% 20%, 20% 80%, 50% 50%;
        transform: translateX(10px);
    }
}

@keyframes lightFloat3 {
    0%, 100% {
        background-position: 50% 0%, 50% 100%, 0% 50%;
        opacity: 0.4;
    }
    50% {
        background-position: 60% 10%, 40% 90%, 10% 60%;
        opacity: 0.7;
    }
}

@keyframes gentleFlow {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 100% 100%;
    }
}
/* Dark Mode Support for Section Backgrounds */
[data-theme="dark"] .section-with-bg::before,
body.dark-theme .section-with-bg::before {
    background:
        radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(168, 85, 247, 0.06) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.05) 0%, transparent 45%);
}

[data-theme="dark"] .section-with-bg::after,
body.dark-theme .section-with-bg::after {
    background:
        linear-gradient(45deg, transparent 40%, rgba(139, 92, 246, 0.04) 60%, transparent 80%);
}

/* Performance Optimizations */
.section-with-bg::before,
.section-with-bg::after {
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
    contain: layout style paint;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .section-with-bg::before {
        animation-duration: 30s, 35s, 32s !important;
        opacity: 0.5;
    }
    .section-with-bg::after {
        animation-duration: 25s !important;
        opacity: 0.4;
    }
}

@media (max-width: 480px) {
    .section-with-bg::before {
        opacity: 0.3;
        animation-duration: 40s, 45s, 42s !important;
    }
    .section-with-bg::after {
        opacity: 0.2;
        animation-duration: 35s !important;
    }
}

/* Accessibility Support */
@media (prefers-reduced-motion: reduce) {
    .section-with-bg::before,
    .section-with-bg::after {
        animation: none !important;
        opacity: 0.2 !important;
    }
}

@media (prefers-contrast: high) {
    .section-with-bg::before,
    .section-with-bg::after {
        opacity: 0.15 !important;
    }
}

/* Low-end device optimization */
@media (max-width: 480px) and (max-resolution: 150dpi) {
    .section-with-bg::before {
        background: radial-gradient(circle at 50% 50%, rgba(75, 0, 130, 0.03) 0%, transparent 60%);
        background-size: 200px 200px;
        animation: simpleFloat 30s ease-in-out infinite;
    }

    .section-with-bg::after {
        display: none;
    }
}

@keyframes simpleFloat {
    0%, 100% {
        background-position: 0% 0%;
        opacity: 0.3;
    }
    50% {
        background-position: 100% 100%;
        opacity: 0.5;
    }
}

/* CPU-intensive animation detection and fallback */
@supports not (contain: layout) {
    .section-with-bg::before,
    .section-with-bg::after {
        animation-duration: 60s !important;
        opacity: 0.2 !important;
    }
}