<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Rating Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #4B0082;
            margin-bottom: 20px;
            text-align: center;
        }
        .rating-demo {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stars {
            color: #ffc107;
        }
        .interactive-stars {
            cursor: pointer;
            user-select: none;
        }
        .interactive-star {
            transition: all 0.2s ease;
            cursor: pointer;
            margin-right: 2px;
        }
        .interactive-star:hover {
            transform: scale(1.1);
            color: #ffb400 !important;
        }
        .interactive-star.highlighted {
            color: #ffb400 !important;
            transform: scale(1.05);
        }
        .interactive-star.rated {
            color: #ff6b35 !important;
            text-shadow: 0 0 5px rgba(255, 107, 53, 0.3);
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .rating-status {
            margin-top: 10px;
            font-weight: bold;
        }
        .success { color: #27ae60; }
        .info { color: #3498db; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🌟 Interactive Rating System Test</h1>
        
        <div class="instructions">
            <h3>How to Test:</h3>
            <ol>
                <li><strong>Hover</strong> over the stars to see the highlight effect</li>
                <li><strong>Click</strong> on a star to rate the product</li>
                <li><strong>Watch</strong> for the visual feedback and notification</li>
                <li><strong>Try rating</strong> different products to see individual ratings</li>
            </ol>
        </div>

        <div class="rating-demo">
            <strong>Product 1:</strong>
            <div class="stars interactive-stars" data-product-id="1">
                <i class="far fa-star interactive-star" data-rating="1" data-product-id="1"></i>
                <i class="far fa-star interactive-star" data-rating="2" data-product-id="1"></i>
                <i class="far fa-star interactive-star" data-rating="3" data-product-id="1"></i>
                <i class="far fa-star interactive-star" data-rating="4" data-product-id="1"></i>
                <i class="far fa-star interactive-star" data-rating="5" data-product-id="1"></i>
            </div>
            <span class="rating-status" id="status1">Click to rate</span>
        </div>

        <div class="rating-demo">
            <strong>Product 2:</strong>
            <div class="stars interactive-stars" data-product-id="2">
                <i class="far fa-star interactive-star" data-rating="1" data-product-id="2"></i>
                <i class="far fa-star interactive-star" data-rating="2" data-product-id="2"></i>
                <i class="far fa-star interactive-star" data-rating="3" data-product-id="2"></i>
                <i class="far fa-star interactive-star" data-rating="4" data-product-id="2"></i>
                <i class="far fa-star interactive-star" data-rating="5" data-product-id="2"></i>
            </div>
            <span class="rating-status" id="status2">Click to rate</span>
        </div>

        <div class="rating-demo">
            <strong>Product 3:</strong>
            <div class="stars interactive-stars" data-product-id="3">
                <i class="far fa-star interactive-star" data-rating="1" data-product-id="3"></i>
                <i class="far fa-star interactive-star" data-rating="2" data-product-id="3"></i>
                <i class="far fa-star interactive-star" data-rating="3" data-product-id="3"></i>
                <i class="far fa-star interactive-star" data-rating="4" data-product-id="3"></i>
                <i class="far fa-star interactive-star" data-rating="5" data-product-id="3"></i>
            </div>
            <span class="rating-status" id="status3">Click to rate</span>
        </div>

        <div class="instructions">
            <h3>✅ Features Working:</h3>
            <ul>
                <li class="success">✓ Hover effects on stars</li>
                <li class="success">✓ Click to rate functionality</li>
                <li class="success">✓ Visual feedback with color changes</li>
                <li class="success">✓ Individual product rating storage</li>
                <li class="success">✓ Persistent ratings (stored in localStorage)</li>
                <li class="success">✓ Rating status display</li>
            </ul>
        </div>

        <div class="instructions">
            <h3>🔗 Test on Actual Pages:</h3>
            <p>Now try the interactive rating on the actual product pages:</p>
            <ul>
                <li><a href="index.html" target="_blank">Homepage - Featured Products</a></li>
                <li><a href="products.html" target="_blank">Products Page - All Products</a></li>
                <li><a href="sale.html" target="_blank">Sale Page - Sale Products</a></li>
            </ul>
        </div>
    </div>

    <script>
        // Initialize interactive rating for all star containers
        document.querySelectorAll('.interactive-stars').forEach(container => {
            const productId = container.getAttribute('data-product-id');
            setupInteractiveRating(container, productId);
            
            // Load existing rating if any
            const existingRating = getUserProductRating(productId);
            if (existingRating) {
                const stars = container.querySelectorAll('.interactive-star');
                updateStarDisplay(stars, existingRating);
                document.getElementById(`status${productId}`).textContent = `You rated: ${existingRating} star${existingRating > 1 ? 's' : ''}`;
                document.getElementById(`status${productId}`).className = 'rating-status success';
            }
        });

        // Setup interactive rating functionality
        function setupInteractiveRating(starsContainer, productId) {
            const stars = starsContainer.querySelectorAll('.interactive-star');
            
            stars.forEach((star, index) => {
                // Hover effect
                star.addEventListener('mouseenter', () => {
                    highlightStarsUpTo(stars, index + 1);
                });
                
                // Click to rate
                star.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const rating = index + 1;
                    submitProductRating(productId, rating);
                    updateStarDisplay(stars, rating);
                    document.getElementById(`status${productId}`).textContent = `You rated: ${rating} star${rating > 1 ? 's' : ''}`;
                    document.getElementById(`status${productId}`).className = 'rating-status success';
                    showSimpleNotification(`You rated Product ${productId} with ${rating} star${rating > 1 ? 's' : ''}!`);
                });
            });
            
            // Reset on mouse leave
            starsContainer.addEventListener('mouseleave', () => {
                const userRating = getUserProductRating(productId);
                if (userRating) {
                    updateStarDisplay(stars, userRating);
                } else {
                    resetStarDisplay(stars);
                }
            });
        }

        // Highlight stars up to a certain rating
        function highlightStarsUpTo(stars, rating) {
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.className = 'fas fa-star interactive-star highlighted';
                } else {
                    star.className = 'far fa-star interactive-star';
                }
            });
        }

        // Update star display after rating
        function updateStarDisplay(stars, rating) {
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.className = 'fas fa-star interactive-star rated';
                } else {
                    star.className = 'far fa-star interactive-star';
                }
            });
        }

        // Reset star display
        function resetStarDisplay(stars) {
            stars.forEach(star => {
                star.className = star.className.replace(' highlighted', '').replace(' rated', '');
            });
        }

        // Submit product rating
        function submitProductRating(productId, rating) {
            const userRatings = JSON.parse(localStorage.getItem('userProductRatings')) || {};
            userRatings[productId] = {
                rating: rating,
                date: new Date().toISOString()
            };
            localStorage.setItem('userProductRatings', JSON.stringify(userRatings));
        }

        // Get user's rating for a specific product
        function getUserProductRating(productId) {
            const userRatings = JSON.parse(localStorage.getItem('userProductRatings')) || {};
            return userRatings[productId] ? userRatings[productId].rating : null;
        }

        // Simple notification function
        function showSimpleNotification(message) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4B0082;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 1000;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
